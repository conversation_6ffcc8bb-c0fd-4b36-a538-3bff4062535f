<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态多边形创建器 (拖拽版)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        /* 自定义光标 */
        .cursor-crosshair { cursor: crosshair; }
        .cursor-pointer { cursor: pointer; }
        .cursor-move { cursor: move; }
        #square-canvas { cursor: grab; }
        #square-canvas:active { cursor: grabbing; }
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-800 flex flex-col items-center justify-center min-h-screen p-4">

    <div class="w-full max-w-6xl bg-white rounded-xl shadow-lg p-6">
        <h1 class="text-2xl font-bold text-center mb-2">动态多边形和方块创建器 (拖拽版)</h1>
        <p class="text-center text-gray-500 mb-4">先绘制多边形，然后可选择自动填充或手动添加方块。</p>
        
        <!-- 控制按钮 -->
        <div class="flex flex-wrap justify-center items-center gap-3 mb-4">
            <button id="toggle-draw-btn" class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded-lg shadow transition-transform transform hover:scale-105">
                <span class="step-number bg-white text-blue-500 rounded-full w-6 h-6 inline-flex items-center justify-center mr-2">1</span>开始绘制
            </button>
            <button id="fill-btn" class="bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg shadow transition-transform transform hover:scale-105" disabled>
                开始填充
            </button>
            <button id="align-horizontal-btn" class="bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-4 rounded-lg shadow transition-transform transform hover:scale-105" disabled>
                水平对齐
            </button>
            <button id="align-vertical-btn" class="bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-4 rounded-lg shadow transition-transform transform hover:scale-105" disabled>
                垂直对齐
            </button>
            <button id="clear-btn" class="bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded-lg shadow transition-transform transform hover:scale-105">
                 清除所有
            </button>
        </div>

        <!-- 状态提示 -->
        <div id="status-bar" class="text-center mb-4 p-2 bg-gray-200 rounded-lg text-gray-700 font-medium">
            请点击“开始绘制”按钮来定义多边形区域。
        </div>

        <!-- 主内容区：画布 + 方块面板 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Canvas 画布 -->
            <div class="md:col-span-2 w-full h-[50vh] md:h-[60vh] bg-gray-50 border-2 border-gray-300 rounded-lg shadow-inner overflow-hidden">
                <canvas id="main-canvas"></canvas>
            </div>

            <!-- 方块定制面板 -->
            <div class="md:col-span-1 bg-gray-50 p-4 rounded-lg border-2 border-gray-300 shadow-inner">
                <h2 class="text-lg font-bold text-center mb-4">手动添加方块 (柱子)</h2>
                <div class="space-y-4">
                    <div>
                        <label for="square-width" class="block text-sm font-medium text-gray-700">宽度 (px)</label>
                        <input type="number" id="square-width" value="30" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2">
                    </div>
                    <div>
                        <label for="square-height" class="block text-sm font-medium text-gray-700">高度 (px)</label>
                        <input type="number" id="square-height" value="20" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm p-2">
                    </div>
                </div>
                <p class="text-center text-xs text-gray-500 mt-4">从下方区域拖动红色方块到左侧画布</p>
                <div class="w-full h-48 mt-2 bg-white border border-dashed border-gray-400 rounded-lg flex items-center justify-center">
                    <canvas id="square-canvas" draggable="true"></canvas>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- 元素获取 ---
        const mainCanvas = document.getElementById('main-canvas');
        const mainCtx = mainCanvas.getContext('2d');
        const mainCanvasContainer = mainCanvas.parentElement;
        const squareCanvas = document.getElementById('square-canvas');
        const squareCtx = squareCanvas.getContext('2d');
        const widthInput = document.getElementById('square-width');
        const heightInput = document.getElementById('square-height');
        const toggleDrawBtn = document.getElementById('toggle-draw-btn');
        const clearBtn = document.getElementById('clear-btn');
        const fillBtn = document.getElementById('fill-btn');
        const alignHorizontalBtn = document.getElementById('align-horizontal-btn');
        const alignVerticalBtn = document.getElementById('align-vertical-btn');
        const statusBar = document.getElementById('status-bar');

        // --- 状态变量 ---
        let mode = 'idle'; // 'idle', 'drawingPolygon'
        let currentPolygonPoints = [];
        let polygons = [];
        let squares = []; // 用户手动添加的方块 (柱子)
        let borderRects = []; // 边缘填充的方块
        let innerFillRects = []; // 内部填充的方块
        let aisleRects = []; // 车道区域
        let mousePos = { x: 0, y: 0 };
        const closingRadius = 15;

        // 方块交互状态
        let selectedSquareIndices = [];
        let isDragging = false;
        let dragStartMousePos = { x: 0, y: 0 };
        let dragStartSquarePositions = [];

        // --- 初始化 ---
        function init() {
            resizeMainCanvas();
            setupEventListeners();
            updateSquareCanvas();
            drawMainCanvas();
        }

        function setupEventListeners() {
            window.addEventListener('resize', resizeMainCanvas);
            window.addEventListener('keydown', handleKeyDown);
            
            mainCanvas.addEventListener('mousedown', handleMouseDown);
            mainCanvas.addEventListener('mousemove', handleMouseMove);
            mainCanvas.addEventListener('mouseup', handleMouseUp);
            mainCanvas.addEventListener('dragover', handleDragOver);
            mainCanvas.addEventListener('drop', handleDrop);

            toggleDrawBtn.addEventListener('click', toggleDrawingMode);
            clearBtn.addEventListener('click', clearAll);
            fillBtn.addEventListener('click', startFilling);
            alignHorizontalBtn.addEventListener('click', alignHorizontally);
            alignVerticalBtn.addEventListener('click', alignVertically);

            widthInput.addEventListener('input', updateSquareCanvas);
            heightInput.addEventListener('input', updateSquareCanvas);
            squareCanvas.addEventListener('dragstart', handleDragStart);
        }

        function resizeMainCanvas() {
            mainCanvas.width = mainCanvasContainer.clientWidth;
            mainCanvas.height = mainCanvasContainer.clientHeight;
            drawMainCanvas();
        }

        // --- 核心绘制函数 ---
        function updateSquareCanvas() {
            const width = parseInt(widthInput.value) || 30;
            const height = parseInt(heightInput.value) || 20;
            squareCanvas.width = width + 20;
            squareCanvas.height = height + 20;
            squareCtx.clearRect(0, 0, squareCanvas.width, squareCanvas.height);
            drawSquare(squareCtx, squareCanvas.width / 2, squareCanvas.height / 2, width, height, '#ef4444', '#b91c1c');
        }

        function drawMainCanvas() {
            mainCtx.clearRect(0, 0, mainCanvas.width, mainCanvas.height);
            polygons.forEach(p => drawPolygon(p, '#0ea5e9', '#bae6fd', true));
            if (currentPolygonPoints.length > 0) {
                drawPolygon(currentPolygonPoints, '#0ea5e9', 'rgba(186, 230, 253, 0.5)', false);
                if (mode === 'drawingPolygon') {
                    drawGuideline();
                    drawClosingHint();
                }
            }
            // 绘制车道区域（浅灰色）
            aisleRects.forEach(rect => {
                mainCtx.save();
                mainCtx.translate(rect.x, rect.y);
                mainCtx.rotate(rect.angle);
                drawSquare(mainCtx, 0, 0, rect.width, rect.height, '#e5e7eb', '#d1d5db'); // Light gray for aisles
                mainCtx.restore();
            });

            // 绘制停车位（蓝色）
            const allRects = [...borderRects, ...innerFillRects];
            allRects.forEach(rect => {
                mainCtx.save();
                mainCtx.translate(rect.x, rect.y);
                mainCtx.rotate(rect.angle);
                drawSquare(mainCtx, 0, 0, rect.width, rect.height, '#3b82f6', '#1d4ed8'); // Blue color
                mainCtx.restore();
            });
            squares.forEach((square, index) => {
                const isSelected = selectedSquareIndices.includes(index);
                const strokeColor = isSelected ? '#3b82f6' : '#b91c1c';
                const lineWidth = isSelected ? 3 : 2;
                drawSquare(mainCtx, square.x, square.y, square.width, square.height, '#ef4444', strokeColor, lineWidth);
            });
        }

        // --- 辅助绘制函数 ---
        function drawSquare(ctx, x, y, w, h, fill, stroke, lineWidth = 2) {
            ctx.fillStyle = fill;
            ctx.strokeStyle = stroke;
            ctx.lineWidth = lineWidth;
            ctx.fillRect(x - w / 2, y - h / 2, w, h);
            ctx.strokeRect(x - w / 2, y - h / 2, w, h);
        }

        function drawPolygon(points, stroke, fill, isClosed) {
            if (points.length < 1) return;
            mainCtx.fillStyle = fill;
            mainCtx.strokeStyle = stroke;
            mainCtx.lineWidth = 3;
            mainCtx.beginPath();
            mainCtx.moveTo(points[0].x, points[0].y);
            for (let i = 1; i < points.length; i++) mainCtx.lineTo(points[i].x, points[i].y);
            if (isClosed) mainCtx.closePath();
            mainCtx.fill();
            mainCtx.stroke();
            points.forEach(p => {
                mainCtx.beginPath();
                mainCtx.arc(p.x, p.y, 5, 0, Math.PI * 2);
                mainCtx.fillStyle = '#ffffff';
                mainCtx.fill();
                mainCtx.strokeStyle = stroke;
                mainCtx.lineWidth = 2;
                mainCtx.stroke();
            });
        }
        
        function drawGuideline() {
            const lastPoint = currentPolygonPoints[currentPolygonPoints.length - 1];
            mainCtx.beginPath();
            mainCtx.moveTo(lastPoint.x, lastPoint.y);
            mainCtx.lineTo(mousePos.x, mousePos.y);
            mainCtx.strokeStyle = 'rgba(14, 165, 233, 0.5)';
            mainCtx.lineWidth = 2;
            mainCtx.setLineDash([5, 5]);
            mainCtx.stroke();
            mainCtx.setLineDash([]);
        }

        function drawClosingHint() {
            if (currentPolygonPoints.length < 3) return;
            const firstPoint = currentPolygonPoints[0];
            if (isNearPoint(mousePos, firstPoint, closingRadius)) {
                mainCtx.beginPath();
                mainCtx.arc(firstPoint.x, firstPoint.y, closingRadius, 0, Math.PI * 2);
                mainCtx.fillStyle = 'rgba(22, 163, 74, 0.3)';
                mainCtx.fill();
            }
        }

        // --- 事件处理 ---
        function handleMouseDown(event) {
            const pos = getCanvasMousePos(event);
            if (mode === 'drawingPolygon') {
                if (currentPolygonPoints.length > 2 && isNearPoint(pos, currentPolygonPoints[0], closingRadius)) {
                    finishDrawing();
                } else {
                    currentPolygonPoints.push(pos);
                }
            } else {
                const clickedSquareIndex = getClickedSquareIndex(pos);
                if (event.shiftKey) {
                    const indexInSelection = selectedSquareIndices.indexOf(clickedSquareIndex);
                    if (clickedSquareIndex !== -1) {
                        if (indexInSelection > -1) selectedSquareIndices.splice(indexInSelection, 1);
                        else selectedSquareIndices.push(clickedSquareIndex);
                    }
                } else {
                    if (clickedSquareIndex !== -1) {
                        if (!selectedSquareIndices.includes(clickedSquareIndex)) {
                            selectedSquareIndices = [clickedSquareIndex];
                        }
                        isDragging = true;
                        dragStartMousePos = pos;
                        dragStartSquarePositions = selectedSquareIndices.map(i => ({...squares[i]}));
                    } else {
                        selectedSquareIndices = [];
                    }
                }
            }
            updateUI();
            drawMainCanvas();
        }

        function handleMouseMove(event) {
            const pos = getCanvasMousePos(event);
            mousePos = pos;
            if (mode === 'drawingPolygon') {
                updateCursorForPolygon();
            } else if (isDragging && selectedSquareIndices.length > 0) {
                const dx = pos.x - dragStartMousePos.x;
                const dy = pos.y - dragStartMousePos.y;
                selectedSquareIndices.forEach((originalIndex, i) => {
                    squares[originalIndex].x = dragStartSquarePositions[i].x + dx;
                    squares[originalIndex].y = dragStartSquarePositions[i].y + dy;
                });
            } else {
                updateCursorForSquares(pos);
            }
            drawMainCanvas();
        }

        function handleMouseUp() {
            isDragging = false;
        }

        function handleKeyDown(event) {
            if ((event.key === 'Delete' || event.key === 'Backspace') && selectedSquareIndices.length > 0) {
                selectedSquareIndices.sort((a, b) => b - a).forEach(index => squares.splice(index, 1));
                selectedSquareIndices = [];
                updateUI();
                drawMainCanvas();
            }
        }
        
        function toggleDrawingMode() {
            if (mode === 'drawingPolygon') {
                finishDrawing(true);
            } else {
                mode = 'drawingPolygon';
                currentPolygonPoints = [];
                polygons = [];
                squares = [];
                borderRects = [];
                innerFillRects = [];
                aisleRects = [];
                selectedSquareIndices = [];
            }
            updateUI();
            drawMainCanvas();
        }

        function finishDrawing(force = false) {
            if (currentPolygonPoints.length > 2) {
                 polygons.push(currentPolygonPoints);
                 currentPolygonPoints = [];
                 mode = 'idle';
            } else if (force && currentPolygonPoints.length > 0) {
                 currentPolygonPoints = [];
                 mode = 'idle';
            } else if(!force) {
                 statusBar.textContent = '一个多边形至少需要3个顶点！';
                 statusBar.classList.add('text-red-600');
                 setTimeout(() => {
                    statusBar.classList.remove('text-red-600');
                    updateUI();
                 }, 2000);
            }
            updateUI();
            drawMainCanvas();
        }

        function clearAll() {
            mode = 'idle';
            currentPolygonPoints = [];
            polygons = [];
            squares = [];
            borderRects = [];
            innerFillRects = [];
            aisleRects = [];
            selectedSquareIndices = [];
            updateUI();
            drawMainCanvas();
        }

        // --- 自动填充功能 (严格按照分层填充算法) ---
        function startFilling() {
            if (polygons.length === 0) return;

            borderRects = [];
            innerFillRects = [];

            const polygon = polygons[0];
            const L = 30; // 车位长度
            const W_spot = 20; // 车位宽度
            const W_aisle = 25; // 车道宽度
            const innerPolygonOffset = L + W_aisle; // 偏移距离 = 车位长度 + 车道宽度

            // --- 第1步：生成外围停车排 ---
            generatePerimeterParking(polygon, L, W_spot);

            // --- 第2步：定义核心停车区 ---
            const inwardSign = getPolygonOrientation(polygon);
            const innerPolygon = createInnerPolygon(polygon, innerPolygonOffset, inwardSign);
            if (!innerPolygon || innerPolygon.length < 3) {
                drawMainCanvas();
                return;
            }

            // --- 第3步：生成首个核心停车模块 ---
            const longestEdgeIndex = findLongestEdge(innerPolygon);
            if (longestEdgeIndex === -1) {
                console.log("未找到最长边");
                drawMainCanvas();
                return;
            }

            const p1 = innerPolygon[longestEdgeIndex];
            const p2 = innerPolygon[(longestEdgeIndex + 1) % innerPolygon.length];
            const mainAxis = {
                start: p1,
                end: p2,
                angle: Math.atan2(p2.y - p1.y, p2.x - p1.x)
            };

            console.log("主轴:", mainAxis);

            // 生成首个双排停车模块
            const initialModule = generateInitialCoreModule(innerPolygon, mainAxis, L, W_spot);
            if (!initialModule) {
                console.log("无法生成初始模块");
                drawMainCanvas();
                return;
            }

            console.log("初始模块生成成功，停车位数量:", initialModule.spots.length);

            // --- 第4步：附加车道 ---
            const moduleWithAisle = attachAisle(initialModule, innerPolygon, W_aisle);

            // --- 第5步：迭代填充剩余空间 ---
            iterativeFillRemainingSpace(innerPolygon, moduleWithAisle, mainAxis, L, W_spot, W_aisle);

            drawMainCanvas();
            updateUI();
        }

        // 第1步：生成外围停车排
        function generatePerimeterParking(polygon, L, W_spot) {
            const cornerMargin = L / 2;
            const inwardSign = getPolygonOrientation(polygon);

            for (let i = 0; i < polygon.length; i++) {
                const p1 = polygon[i];
                const p2 = polygon[(i + 1) % polygon.length];
                const dx = p2.x - p1.x;
                const dy = p2.y - p1.y;
                const edgeLen = Math.hypot(dx, dy);
                const angle = Math.atan2(dy, dx);
                let nx = -dy / edgeLen * inwardSign;
                let ny = dx / edgeLen * inwardSign;

                let distanceAlongEdge = cornerMargin;
                while (distanceAlongEdge < edgeLen - cornerMargin) {
                    const edgeCenterX = p1.x + (distanceAlongEdge / edgeLen) * dx;
                    const edgeCenterY = p1.y + (distanceAlongEdge / edgeLen) * dy;
                    const rectCenterX = edgeCenterX + nx * (L / 2);
                    const rectCenterY = edgeCenterY + ny * (L / 2);

                    const parkingSpot = {
                        x: rectCenterX, y: rectCenterY,
                        width: W_spot, height: L,
                        angle: angle
                    };

                    // 验证停车位是否完全在多边形内
                    const vertices = getRotatedVertices(parkingSpot);
                    const isFullyInside = vertices.every(vtx => isPointInPolygon(vtx, polygon));

                    if (isFullyInside) {
                        borderRects.push(parkingSpot);
                    }

                    distanceAlongEdge += W_spot;
                }
            }
        }

        // 获取多边形方向（顺时针或逆时针）
        function getPolygonOrientation(polygon) {
            let area = 0;
            for (let i = 0; i < polygon.length; i++) {
                const p1 = polygon[i];
                const p2 = polygon[(i + 1) % polygon.length];
                area += (p1.x * p2.y - p2.x * p1.y);
            }
            return Math.sign(area);
        }

        // 找到最长边
        function findLongestEdge(polygon) {
            let longestEdgeIndex = -1;
            let maxLenSq = 0;
            for (let i = 0; i < polygon.length; i++) {
                const p1 = polygon[i];
                const p2 = polygon[(i + 1) % polygon.length];
                const lenSq = (p2.x - p1.x)**2 + (p2.y - p1.y)**2;
                if (lenSq > maxLenSq) {
                    maxLenSq = lenSq;
                    longestEdgeIndex = i;
                }
            }
            return longestEdgeIndex;
        }

        // 第3步：生成首个核心停车模块（双排背对背）
        function generateInitialCoreModule(innerPolygon, mainAxis, L, W_spot) {
            const moduleWidth = 2 * L; // 双排停车位的总宽度
            const perpAngle = mainAxis.angle + Math.PI / 2; // 垂直于主轴的角度

            // 计算模块的中心线（沿主轴方向）
            const axisLength = Math.hypot(mainAxis.end.x - mainAxis.start.x, mainAxis.end.y - mainAxis.start.y);
            const axisCenter = {
                x: (mainAxis.start.x + mainAxis.end.x) / 2,
                y: (mainAxis.start.y + mainAxis.end.y) / 2
            };

            // 创建双排停车模块的矩形区域
            const moduleRect = {
                center: axisCenter,
                width: axisLength,
                height: moduleWidth,
                angle: mainAxis.angle
            };

            // 裁剪模块以适应内部多边形
            const clippedModule = clipModuleToPolygon(moduleRect, innerPolygon, L, W_spot, mainAxis.angle);
            return clippedModule;
        }

        // 裁剪模块以适应多边形边界
        function clipModuleToPolygon(moduleRect, polygon, L, W_spot, baseAngle) {
            const parkingSpots = [];
            const perpAngle = baseAngle + Math.PI / 2;
            const cos_base = Math.cos(baseAngle);
            const sin_base = Math.sin(baseAngle);
            const cos_perp = Math.cos(perpAngle);
            const sin_perp = Math.sin(perpAngle);

            // 生成双排背对背停车位
            const numSpotsAlongAxis = Math.max(1, Math.floor(moduleRect.width / W_spot));
            const startOffset = -(numSpotsAlongAxis - 1) * W_spot / 2;

            for (let i = 0; i < numSpotsAlongAxis; i++) {
                const alongAxisOffset = startOffset + i * W_spot;

                // 第一排停车位（朝向一个方向）
                const spot1_x = moduleRect.center.x + alongAxisOffset * cos_base - (L/4) * cos_perp;
                const spot1_y = moduleRect.center.y + alongAxisOffset * sin_base - (L/4) * sin_perp;
                const spot1 = {
                    x: spot1_x, y: spot1_y,
                    width: W_spot, height: L,
                    angle: perpAngle
                };

                // 第二排停车位（背对背，短边邻接）
                // 位置：紧邻第一排的短边，朝向相反方向
                const spot2_x = moduleRect.center.x + alongAxisOffset * cos_base + (L/4) * cos_perp;
                const spot2_y = moduleRect.center.y + alongAxisOffset * sin_base + (L/4) * sin_perp;
                const spot2 = {
                    x: spot2_x, y: spot2_y,
                    width: W_spot, height: L,
                    angle: perpAngle + Math.PI // 背对背，旋转180度
                };

                // 验证停车位是否完全在多边形内
                const vertices1 = getRotatedVertices(spot1);
                const vertices2 = getRotatedVertices(spot2);

                if (vertices1.every(vtx => isPointInPolygon(vtx, polygon))) {
                    parkingSpots.push(spot1);
                }
                if (vertices2.every(vtx => isPointInPolygon(vtx, polygon))) {
                    parkingSpots.push(spot2);
                }
            }

            return {
                spots: parkingSpots,
                bounds: moduleRect,
                baseAngle: baseAngle
            };
        }

        // 第4步：附加车道
        function attachAisle(module, innerPolygon, W_aisle) {
            // 在模块的一侧附加车道
            const moduleWidth = module.bounds ? module.bounds.height : (2 * 30); // 默认双排宽度

            // 创建车道矩形
            if (module.bounds) {
                const aisleRect = {
                    x: module.bounds.center.x,
                    y: module.bounds.center.y,
                    width: module.bounds.width,
                    height: W_aisle,
                    angle: module.bounds.angle
                };

                // 将车道矩形偏移到模块的一侧
                const perpAngle = module.bounds.angle + Math.PI / 2;
                const offsetDistance = (moduleWidth + W_aisle) / 2;
                aisleRect.x += offsetDistance * Math.cos(perpAngle);
                aisleRect.y += offsetDistance * Math.sin(perpAngle);

                // 裁剪车道以适应内部多边形
                const aisleVertices = getRotatedVertices(aisleRect);
                const isFullyInside = aisleVertices.every(vtx => isPointInPolygon(vtx, innerPolygon));

                if (isFullyInside) {
                    aisleRects.push(aisleRect);
                }
            }

            return {
                ...module,
                aisleWidth: W_aisle,
                totalWidth: moduleWidth + W_aisle
            };
        }

        function createInnerPolygon(polygon, offset, inwardSign) {
            const offsetLines = [];
            for (let i = 0; i < polygon.length; i++) {
                const p1 = polygon[i];
                const p2 = polygon[(i + 1) % polygon.length];
                const dx = p2.x - p1.x;
                const dy = p2.y - p1.y;
                const len = Math.hypot(dx, dy);
                let nx = -dy / len * inwardSign;
                let ny = dx / len * inwardSign;
                offsetLines.push({
                    p1: { x: p1.x + nx * offset, y: p1.y + ny * offset },
                    p2: { x: p2.x + nx * offset, y: p2.y + ny * offset }
                });
            }

            const innerPolygon = [];
            for (let i = 0; i < offsetLines.length; i++) {
                const l1 = offsetLines[i];
                const l2 = offsetLines[(i + 1) % offsetLines.length];
                const intersection = getLineIntersection(l1.p1, l1.p2, l2.p1, l2.p2);
                if (intersection) {
                    innerPolygon.push(intersection);
                }
            }
            return innerPolygon;
        }

        // 第5步：迭代填充剩余空间
        function iterativeFillRemainingSpace(innerPolygon, initialModule, mainAxis, L, W_spot, W_aisle) {
            // 将初始模块的停车位添加到结果中
            innerFillRects.push(...initialModule.spots);

            // 计算剩余空间并在两侧迭代填充
            const perpAngle = mainAxis.angle + Math.PI / 2;
            const cos_perp = Math.cos(perpAngle);
            const sin_perp = Math.sin(perpAngle);

            // 从初始模块的两侧开始填充
            fillOneSide(innerPolygon, initialModule, mainAxis, L, W_spot, W_aisle, 1);  // 正方向
            fillOneSide(innerPolygon, initialModule, mainAxis, L, W_spot, W_aisle, -1); // 负方向
        }

        // 在一侧填充停车模块
        function fillOneSide(innerPolygon, referenceModule, mainAxis, L, W_spot, W_aisle, direction) {
            const perpAngle = mainAxis.angle + Math.PI / 2;
            const cos_perp = Math.cos(perpAngle);
            const sin_perp = Math.sin(perpAngle);

            // 获取参考模块的中心和总宽度
            const refCenter = referenceModule.bounds ? referenceModule.bounds.center : referenceModule.center;
            const refTotalWidth = referenceModule.totalWidth || (2 * L + W_aisle);

            // 计算下一个模块的位置
            let currentOffset = (refTotalWidth / 2) * direction;

            while (true) {
                // 计算下一个模块的中心位置
                const nextModuleOffset = currentOffset + (2 * L + W_aisle) / 2 * direction;
                const nextCenter = {
                    x: refCenter.x + nextModuleOffset * cos_perp,
                    y: refCenter.y + nextModuleOffset * sin_perp
                };

                // 检查剩余空间是否足够放置双排模块
                const doubleRowWidth = 2 * L;
                if (checkSpaceAvailable(innerPolygon, nextCenter, doubleRowWidth, mainAxis)) {
                    // 生成双排停车模块
                    const newModule = generateModuleAtPosition(innerPolygon, nextCenter, mainAxis, L, W_spot, true);
                    if (newModule && newModule.spots.length > 0) {
                        innerFillRects.push(...newModule.spots);
                        currentOffset = nextModuleOffset + (doubleRowWidth + W_aisle) / 2 * direction;
                        continue;
                    }
                }

                // 如果无法放置双排，尝试单排
                const singleRowWidth = L;
                const singleRowOffset = currentOffset + (singleRowWidth + W_aisle) / 2 * direction;
                const singleRowCenter = {
                    x: refCenter.x + singleRowOffset * cos_perp,
                    y: refCenter.y + singleRowOffset * sin_perp
                };

                if (checkSpaceAvailable(innerPolygon, singleRowCenter, singleRowWidth, mainAxis)) {
                    const singleModule = generateModuleAtPosition(innerPolygon, singleRowCenter, mainAxis, L, W_spot, false);
                    if (singleModule && singleModule.spots.length > 0) {
                        innerFillRects.push(...singleModule.spots);
                        currentOffset = singleRowOffset + (singleRowWidth + W_aisle) / 2 * direction;
                        continue;
                    }
                }

                // 无法放置任何模块，结束此方向的填充
                break;
            }
        }

        // 检查指定位置是否有足够空间
        function checkSpaceAvailable(polygon, center, requiredWidth, mainAxis) {
            const perpAngle = mainAxis.angle + Math.PI / 2;
            const cos_perp = Math.cos(perpAngle);
            const sin_perp = Math.sin(perpAngle);

            // 检查中心点周围的空间
            const testPoints = [
                { x: center.x - requiredWidth/2 * cos_perp, y: center.y - requiredWidth/2 * sin_perp },
                { x: center.x + requiredWidth/2 * cos_perp, y: center.y + requiredWidth/2 * sin_perp },
                center
            ];

            return testPoints.every(point => isPointInPolygon(point, polygon));
        }

        // 在指定位置生成停车模块
        function generateModuleAtPosition(polygon, center, mainAxis, L, W_spot, isDoubleRow) {
            const parkingSpots = [];
            const perpAngle = mainAxis.angle + Math.PI / 2;
            const cos_base = Math.cos(mainAxis.angle);
            const sin_base = Math.sin(mainAxis.angle);
            const cos_perp = Math.cos(perpAngle);
            const sin_perp = Math.sin(perpAngle);

            // 计算沿主轴方向的有效长度（基于多边形边界）
            const axisLength = Math.hypot(mainAxis.end.x - mainAxis.start.x, mainAxis.end.y - mainAxis.start.y);
            const maxSpotsAlongAxis = Math.max(1, Math.floor(axisLength / W_spot));
            const startOffset = -(maxSpotsAlongAxis - 1) * W_spot / 2;

            for (let i = 0; i < maxSpotsAlongAxis; i++) {
                const alongAxisOffset = startOffset + i * W_spot;

                // 第一排停车位
                const spot1_x = center.x + alongAxisOffset * cos_base - (L/4) * cos_perp;
                const spot1_y = center.y + alongAxisOffset * sin_base - (L/4) * sin_perp;
                const spot1 = {
                    x: spot1_x, y: spot1_y,
                    width: W_spot, height: L,
                    angle: perpAngle
                };

                const vertices1 = getRotatedVertices(spot1);
                if (vertices1.every(vtx => isPointInPolygon(vtx, polygon))) {
                    parkingSpots.push(spot1);
                }

                // 如果是双排，添加背对背的第二排
                if (isDoubleRow) {
                    // 第二排停车位（背对背，短边邻接）
                    const spot2_x = center.x + alongAxisOffset * cos_base + (L/4) * cos_perp;
                    const spot2_y = center.y + alongAxisOffset * sin_base + (L/4) * sin_perp;
                    const spot2 = {
                        x: spot2_x, y: spot2_y,
                        width: W_spot, height: L,
                        angle: perpAngle + Math.PI // 背对背，旋转180度
                    };

                    const vertices2 = getRotatedVertices(spot2);
                    if (vertices2.every(vtx => isPointInPolygon(vtx, polygon))) {
                        parkingSpots.push(spot2);
                    }
                }
            }

            return {
                spots: parkingSpots,
                center: center
            };
        }

        function getLineIntersection(p1, p2, p3, p4) {
            const d = (p1.x - p2.x) * (p3.y - p4.y) - (p1.y - p2.y) * (p3.x - p4.x);
            if (d === 0) return null;
            const t = ((p1.x - p3.x) * (p3.y - p4.y) - (p1.y - p3.y) * (p3.x - p4.x)) / d;
            return {
                x: p1.x + t * (p2.x - p1.x),
                y: p1.y + t * (p2.y - p1.y)
            };
        }

        // --- 重叠检测辅助函数 (SAT) ---
        function getRotatedVertices(rect) {
            const w = rect.width / 2;
            const h = rect.height / 2;
            const cos = Math.cos(rect.angle);
            const sin = Math.sin(rect.angle);
            const p1 = { x: rect.x + (-w * cos - -h * sin), y: rect.y + (-w * sin + -h * cos) };
            const p2 = { x: rect.x + ( w * cos - -h * sin), y: rect.y + ( w * sin + -h * cos) };
            const p3 = { x: rect.x + ( w * cos -  h * sin), y: rect.y + ( w * sin +  h * cos) };
            const p4 = { x: rect.x + (-w * cos -  h * sin), y: rect.y + (-w * sin +  h * cos) };
            return [p1, p2, p3, p4];
        }

        function project(vertices, axis) {
            let min = Infinity, max = -Infinity;
            for (const vertex of vertices) {
                const dotProduct = vertex.x * axis.x + vertex.y * axis.y;
                min = Math.min(min, dotProduct);
                max = Math.max(max, dotProduct);
            }
            return { min, max };
        }

        function checkOverlap(rect1, rect2) {
            const vertices1 = getRotatedVertices(rect1);
            const vertices2 = getRotatedVertices(rect2);
            const axes = [
                { x: vertices1[1].x - vertices1[0].x, y: vertices1[1].y - vertices1[0].y },
                { x: vertices1[1].x - vertices1[2].x, y: vertices1[1].y - vertices1[2].y },
                { x: vertices2[0].x - vertices2[3].x, y: vertices2[0].y - vertices2[3].y },
                { x: vertices2[0].x - vertices2[1].x, y: vertices2[0].y - vertices2[1].y }
            ];
            for (const axis of axes) {
                const p1 = project(vertices1, axis);
                const p2 = project(vertices2, axis);
                if (p1.max < p2.min || p2.max < p1.min) return false;
            }
            return true;
        }

        // --- 对齐功能 ---
        function alignHorizontally() {
            if (selectedSquareIndices.length < 2) return;
            const anchorY = squares[selectedSquareIndices[0]].y;
            selectedSquareIndices.forEach(index => { squares[index].y = anchorY; });
            drawMainCanvas();
        }

        function alignVertically() {
            if (selectedSquareIndices.length < 2) return;
            const anchorX = squares[selectedSquareIndices[0]].x;
            selectedSquareIndices.forEach(index => { squares[index].x = anchorX; });
            drawMainCanvas();
        }

        // --- 拖拽放置处理 ---
        function handleDragStart(event) {
            const data = JSON.stringify({ width: widthInput.value, height: heightInput.value });
            event.dataTransfer.setData('application/json', data);
            event.dataTransfer.effectAllowed = 'copy';
        }

        function handleDragOver(event) { event.preventDefault(); }
        
        function handleDrop(event) {
            event.preventDefault();
            borderRects = [];
            innerFillRects = [];
            aisleRects = [];
            const pos = getCanvasMousePos(event);
            const dataString = event.dataTransfer.getData('application/json');
            if (dataString) {
                const data = JSON.parse(dataString);
                squares.push({
                    x: pos.x, y: pos.y,
                    width: parseInt(data.width), height: parseInt(data.height)
                });
                drawMainCanvas();
            }
        }

        // --- 辅助与UI函数 ---
        function getCanvasMousePos(event) {
            const rect = mainCanvas.getBoundingClientRect();
            return { x: event.clientX - rect.left, y: event.clientY - rect.top };
        }
        function isNearPoint(p1, p2, radius) {
            return Math.hypot(p1.x - p2.x, p1.y - p2.y) < radius;
        }
        function isPointInPolygon(point, polygon) {
            let isInside = false;
            for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
                const xi = polygon[i].x, yi = polygon[i].y;
                const xj = polygon[j].x, yj = polygon[j].y;
                const intersect = ((yi > point.y) !== (yj > point.y))
                    && (point.x < (xj - xi) * (point.y - yi) / (yj - yi) + xi);
                if (intersect) isInside = !isInside;
            }
            return isInside;
        }
        function isPointInSquare(point, square) {
            const halfW = square.width / 2;
            const halfH = square.height / 2;
            return point.x >= square.x - halfW && point.x <= square.x + halfW &&
                   point.y >= square.y - halfH && point.y <= square.y + halfH;
        }
        function getClickedSquareIndex(pos) {
            for (let i = squares.length - 1; i >= 0; i--) {
                if (isPointInSquare(pos, squares[i])) return i;
            }
            return -1;
        }

        function updateCursorForPolygon() {
            if (currentPolygonPoints.length > 2 && isNearPoint(mousePos, currentPolygonPoints[0], closingRadius)) {
                mainCanvas.className = 'cursor-pointer';
            } else {
                mainCanvas.className = 'cursor-crosshair';
            }
        }
        
        function updateCursorForSquares(pos) {
            let onSquare = false;
            for (const square of squares) {
                if (isPointInSquare(pos, square)) {
                    onSquare = true;
                    break;
                }
            }
            mainCanvas.className = onSquare ? 'cursor-move' : '';
        }

        function updateUI() {
            const step1Span = `<span class="step-number bg-white text-blue-500 rounded-full w-6 h-6 inline-flex items-center justify-center mr-2">1</span>`;
            if (mode === 'drawingPolygon') {
                toggleDrawBtn.innerHTML = `${step1Span}完成绘制`;
                statusBar.textContent = '请在画布上点击添加顶点。点击起始点可自动闭合图形。';
                updateCursorForPolygon();
            } else {
                toggleDrawBtn.innerHTML = `${step1Span}开始绘制`;
                if (polygons.length > 0) {
                    statusBar.textContent = '多边形已完成！可点击“开始填充”或手动拖入方块。';
                } else {
                    statusBar.textContent = '请点击“开始绘制”按钮来定义多边形区域。';
                }
                mainCanvas.className = '';
            }
            // 更新按钮状态
            fillBtn.disabled = polygons.length === 0;
            alignHorizontalBtn.disabled = selectedSquareIndices.length < 2;
            alignVerticalBtn.disabled = selectedSquareIndices.length < 2;
        }

        // --- 启动 ---
        init();
    </script>

</body>
</html>
